// 服务端专用的 LangGraph 模块
// 这个文件只在 Node.js 环境中运行

import { StateGraph, MemorySaver, Annotation, messagesStateReducer } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, AIMessage, BaseMessage } from '@langchain/core/messages';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

// 环境配置
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
const OPENAI_BASE_URL = process.env.OPENAI_BASE_URL || process.env.NEXT_PUBLIC_OPENAI_BASE_URL || 'https://api.openai.com/v1';

// 交易分析工具定义
const stockAnalysisTool = tool(
  async ({ ticker, analysisType }) => {
    // 模拟股票分析数据
    const mockData = {
      ticker,
      analysisType,
      data: {
        price: Math.random() * 1000 + 100,
        change: (Math.random() - 0.5) * 10,
        volume: Math.floor(Math.random() * 1000000),
        recommendation: ['买入', '持有', '卖出'][Math.floor(Math.random() * 3)],
      },
      timestamp: new Date().toISOString(),
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'stock_analysis',
    description: '分析股票的基本面、技术面或新闻情绪',
    schema: z.object({
      ticker: z.string().describe('股票代码，如NVDA、AAPL等'),
      analysisType: z.enum(['fundamentals', 'technical', 'news', 'sentiment']).describe('分析类型'),
    }),
  }
);

const marketDataTool = tool(
  async ({ ticker, dataType, period }) => {
    // 模拟市场数据
    const mockData = {
      ticker,
      dataType,
      period,
      data: Array.from({ length: 10 }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
        value: Math.random() * 100 + 50,
      })),
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'market_data',
    description: '获取股票的市场数据，包括价格、成交量等',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      dataType: z.enum(['price', 'volume', 'indicators']).describe('数据类型'),
      period: z.enum(['1d', '1w', '1m', '3m', '1y']).describe('时间周期'),
    }),
  }
);

const newsAnalysisTool = tool(
  async ({ ticker, sentiment }) => {
    // 模拟新闻分析
    const mockData = {
      ticker,
      sentiment: sentiment ? 'positive' : 'neutral',
      news: [
        { title: `${ticker} 财报超预期`, sentiment: 'positive', impact: 'high' },
        { title: `市场对 ${ticker} 前景看好`, sentiment: 'positive', impact: 'medium' },
      ],
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'news_analysis',
    description: '分析股票相关新闻的情绪和影响',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      sentiment: z.boolean().optional().describe('是否进行情绪分析'),
    }),
  }
);

const riskAssessmentTool = tool(
  async ({ ticker, portfolio, riskLevel }) => {
    // 模拟风险评估
    const mockData = {
      ticker,
      riskLevel,
      assessment: {
        volatility: Math.random() * 0.5,
        beta: Math.random() * 2,
        sharpeRatio: Math.random() * 3,
        recommendation: riskLevel === 'low' ? '适合保守投资者' : '适合激进投资者',
      },
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'risk_assessment',
    description: '评估投资风险和组合风险',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      portfolio: z.array(z.string()).optional().describe('投资组合中的其他股票'),
      riskLevel: z.enum(['low', 'medium', 'high']).describe('风险偏好'),
    }),
  }
);

// 定义工具集合
const tools = [stockAnalysisTool, marketDataTool, newsAnalysisTool, riskAssessmentTool];

// 创建工具节点
const toolNode = new ToolNode(tools);

// 创建LLM模型
export function createTradingLLM(modelName: string = 'gpt-4o-mini', temperature: number = 0) {
  return new ChatOpenAI({
    modelName,
    temperature,
    openAIApiKey: OPENAI_API_KEY,
    configuration: {
      baseURL: OPENAI_BASE_URL,
    },
  }).bindTools(tools);
}

// 定义状态接口
export interface TradingAgentState {
  messages: BaseMessage[];
  ticker?: string;
  analysisConfig?: any;
  analysisResults?: any;
  tradingDecision?: any;
  riskAssessment?: any;
}

// 创建交易代理状态注解
export const TradingAgentAnnotation = Annotation.Root({
  messages: Annotation({
    reducer: messagesStateReducer,
    default: () => [],
  }),
  ticker: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => '',
  }),
  analysisConfig: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => ({}),
  }),
  analysisResults: Annotation({
    reducer: (x: any, y?: any) => y ? { ...x, ...y } : x,
    default: () => ({}),
  }),
  tradingDecision: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
  riskAssessment: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
});

// 判断是否继续执行的函数
function shouldContinue(state: typeof TradingAgentAnnotation.State) {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  
  // 如果LLM调用了工具，则路由到工具节点
  if (lastMessage.tool_calls?.length) {
    return 'tools';
  }
  
  // 否则结束执行
  return '__end__';
}

// 调用模型的函数
async function callModel(state: typeof TradingAgentAnnotation.State) {
  const model = createTradingLLM();
  
  // 构建系统提示
  const systemPrompt = `你是一个专业的金融交易分析师。你的任务是：
1. 分析股票的基本面、技术面和新闻情绪
2. 评估投资风险
3. 提供明确的交易建议

当前分析的股票代码是: ${state.ticker}

请使用可用的工具来收集和分析数据，然后提供专业的交易建议。`;

  const messages = [
    new HumanMessage(systemPrompt),
    ...state.messages,
  ];

  const response = await model.invoke(messages);
  
  return { messages: [response] };
}

// 创建交易分析工作流
export function createTradingWorkflow() {
  const workflow = new StateGraph(TradingAgentAnnotation)
    // 添加节点
    .addNode('agent', callModel)
    .addNode('tools', toolNode)
    
    // 添加边
    .addEdge('__start__', 'agent')
    .addEdge('tools', 'agent')
    .addConditionalEdges('agent', shouldContinue);

  // 添加内存保存器
  const checkpointer = new MemorySaver();
  
  return workflow.compile({ checkpointer });
}

// 简化的交易代理类
export class TradingAgent {
  private workflow: any;
  
  constructor() {
    this.workflow = createTradingWorkflow();
  }
  
  async analyze(ticker: string, config: any = {}) {
    const initialState = {
      messages: [new HumanMessage(`请分析股票 ${ticker}`)],
      ticker,
      analysisConfig: config,
    };
    
    const result = await this.workflow.invoke(initialState, {
      configurable: { thread_id: `analysis_${ticker}_${Date.now()}` }
    });
    
    return result;
  }
  
  async chat(message: string, threadId: string) {
    const result = await this.workflow.invoke(
      { messages: [new HumanMessage(message)] },
      { configurable: { thread_id: threadId } }
    );
    
    return result;
  }
  
  async getState(threadId: string) {
    return await this.workflow.getState({ configurable: { thread_id: threadId } });
  }
}

// 导出默认实例
export const tradingAgent = new TradingAgent();
