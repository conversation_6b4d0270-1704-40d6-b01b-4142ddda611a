// 客户端兼容的 LangGraph 接口
// 实际的 LangGraph 功能已移至服务端

// 基础类型定义
export interface BaseMessage {
  content: string;
  type?: string;
}

export interface HumanMessage extends BaseMessage {
  type: 'human';
}

export interface AIMessage extends BaseMessage {
  type: 'ai';
  tool_calls?: any[];
}

// 定义状态接口
export interface TradingAgentState {
  messages: BaseMessage[];
  ticker?: string;
  analysisConfig?: any;
  analysisResults?: any;
  tradingDecision?: any;
  riskAssessment?: any;
}

// 客户端兼容的交易代理类
export class TradingAgent {
  async analyze(ticker: string, config: any = {}) {
    // 通过 API 调用服务端分析
    const response = await fetch('/api/langgraph/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ticker, config }),
    });

    if (!response.ok) {
      throw new Error(`分析请求失败: ${response.statusText}`);
    }

    return await response.json();
  }

  async chat(message: string, threadId: string) {
    // 通过 API 调用服务端聊天
    const response = await fetch('/api/langgraph/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, threadId }),
    });

    if (!response.ok) {
      throw new Error(`聊天请求失败: ${response.statusText}`);
    }

    return await response.json();
  }

  async getState(threadId: string) {
    // 通过 API 获取状态
    const response = await fetch(`/api/langgraph/state?threadId=${threadId}`);

    if (!response.ok) {
      throw new Error(`获取状态失败: ${response.statusText}`);
    }

    return await response.json();
  }

  async streamAnalysis(ticker: string, config: any = {}) {
    // 模拟流式分析
    const result = await this.analyze(ticker, config);

    // 返回一个异步生成器
    return (async function* () {
      yield { currentStep: '开始分析...', progress: 0 };
      yield { currentStep: '分析基本面...', progress: 25 };
      yield { currentStep: '分析技术面...', progress: 50 };
      yield { currentStep: '分析情绪...', progress: 75 };
      yield { currentStep: '生成建议...', progress: 100, ...result };
    })();
  }
}

// 创建 HumanMessage 辅助函数
export function createHumanMessage(content: string): HumanMessage {
  return {
    type: 'human',
    content,
  };
}

// 导出默认实例
export const tradingAgent = new TradingAgent();
