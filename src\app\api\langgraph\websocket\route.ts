import { NextRequest, NextResponse } from 'next/server';

// WebSocket 连接管理
const connections = new Map<string, WebSocket>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 检查是否支持 WebSocket 升级
    const upgradeHeader = request.headers.get('upgrade');
    if (upgradeHeader !== 'websocket') {
      return NextResponse.json(
        { error: '此端点需要 WebSocket 连接' },
        { status: 400 }
      );
    }

    // 注意：Next.js API Routes 不直接支持 WebSocket
    // 这里返回连接信息，实际的 WebSocket 服务器需要单独实现
    return NextResponse.json({
      message: 'WebSocket 端点已准备就绪',
      threadId,
      wsUrl: `ws://localhost:8000/ws/analysis/${threadId}`,
    });

  } catch (error) {
    console.error('WebSocket 连接失败:', error);
    return NextResponse.json(
      { error: 'WebSocket 连接失败' },
      { status: 500 }
    );
  }
}

// 发送消息到特定线程的所有连接
export function broadcastToThread(threadId: string, message: any) {
  const connection = connections.get(threadId);
  if (connection && connection.readyState === WebSocket.OPEN) {
    try {
      connection.send(JSON.stringify(message));
    } catch (error) {
      console.error('发送 WebSocket 消息失败:', error);
      connections.delete(threadId);
    }
  }
}

// 广播到所有连接
export function broadcastToAll(message: any) {
  for (const [threadId, connection] of connections.entries()) {
    if (connection.readyState === WebSocket.OPEN) {
      try {
        connection.send(JSON.stringify(message));
      } catch (error) {
        console.error('发送 WebSocket 消息失败:', error);
        connections.delete(threadId);
      }
    } else {
      connections.delete(threadId);
    }
  }
}
