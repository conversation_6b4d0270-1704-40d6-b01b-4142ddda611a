'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import toast from 'react-hot-toast';
import { langGraphClient, type ChatMessage } from '@/lib/langgraph-client';
import { useWebSocket } from './useWebSocket';

// 使用客户端定义的类型
type AgentMessage = ChatMessage;

interface AgentState {
  messages: AgentMessage[];
  isProcessing: boolean;
  currentStep: string;
  analysisResults: any;
  tradingDecision: any;
  error: string | null;
  progress: number;
  isStreaming: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  sessionInfo: {
    createdAt?: Date;
    updatedAt?: Date;
    messageCount: number;
  };
}

export function useLangGraphAgent() {
  const [state, setState] = useState<AgentState>({
    messages: [],
    isProcessing: false,
    currentStep: '',
    analysisResults: null,
    tradingDecision: null,
    error: null,
    progress: 0,
    isStreaming: false,
    connectionStatus: 'disconnected',
    sessionInfo: {
      messageCount: 0,
    },
  });

  const threadIdRef = useRef<string>('');
  const wsUrlRef = useRef<string | null>(null);

  // WebSocket 连接用于实时状态更新
  const { isConnected, sendMessage: sendWsMessage } = useWebSocket(
    wsUrlRef.current,
    {
      onMessage: (message) => {
        // 处理来自后端的实时状态更新
        if (message.type === 'sessionUpdate') {
          setState(prev => ({
            ...prev,
            currentStep: message.payload.currentStep || prev.currentStep,
            isProcessing: message.payload.isProcessing ?? prev.isProcessing,
            error: message.payload.error || prev.error,
            sessionInfo: {
              ...prev.sessionInfo,
              updatedAt: new Date(message.payload.updatedAt),
            },
          }));
        }
      },
      onOpen: () => {
        setState(prev => ({ ...prev, connectionStatus: 'connected' }));
      },
      onClose: () => {
        setState(prev => ({ ...prev, connectionStatus: 'disconnected' }));
      },
      onError: () => {
        setState(prev => ({ ...prev, connectionStatus: 'error' }));
      },
    }
  );

  // 更新连接状态
  useEffect(() => {
    setState(prev => ({
      ...prev,
      connectionStatus: isConnected ? 'connected' : 'disconnected',
    }));
  }, [isConnected]);

  // 生成新的线程ID
  const generateThreadId = useCallback(() => {
    const id = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    threadIdRef.current = id;
    return id;
  }, []);

  // 添加消息到状态
  const addMessage = useCallback((message: Omit<AgentMessage, 'id' | 'timestamp'>) => {
    const newMessage: AgentMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: new Date(),
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
    }));

    return newMessage;
  }, []);

  // 更新当前步骤
  const updateStep = useCallback((step: string) => {
    setState(prev => ({ ...prev, currentStep: step }));
  }, []);

  // 开始股票分析
  const analyzeStock = useCallback(async (ticker: string, config: any = {}) => {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null,
        currentStep: '初始化分析',
      }));

      // 生成新的线程ID
      const threadId = generateThreadId();

      // 添加用户消息
      addMessage({
        type: 'human',
        content: `请分析股票 ${ticker}`,
        metadata: { ticker, config },
      });

      updateStep('正在分析股票数据...');

      // 使用客户端进行分析
      const result = await langGraphClient.analyzeStock(ticker, config, threadId);

      // 处理结果
      if (result.content) {
        addMessage({
          type: 'ai',
          content: result.content,
          metadata: result,
        });
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: '分析完成',
        analysisResults: result.analysisResults,
        tradingDecision: result.tradingDecision,
      }));

      toast.success('股票分析完成');
      return result;

    } catch (error) {
      console.error('分析失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';

      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
        currentStep: '分析失败',
      }));

      addMessage({
        type: 'system',
        content: `分析失败: ${errorMessage}`,
      });

      toast.error(`分析失败: ${errorMessage}`);
      throw error;
    }
  }, [addMessage, updateStep, generateThreadId]);

  // 发送聊天消息
  const sendMessage = useCallback(async (message: string) => {
    try {
      setState(prev => ({ ...prev, isProcessing: true, error: null }));

      // 添加用户消息
      addMessage({
        type: 'human',
        content: message,
      });

      // 如果没有线程ID，生成一个
      if (!threadIdRef.current) {
        generateThreadId();
      }

      updateStep('处理消息...');

      // 使用客户端发送消息
      const result = await langGraphClient.sendMessage(message, threadIdRef.current);

      // 添加AI回复
      if (result.content) {
        addMessage({
          type: 'ai',
          content: result.content,
          metadata: result,
        });
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: '消息处理完成',
      }));

      return result;

    } catch (error) {
      console.error('消息发送失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
      }));

      addMessage({
        type: 'system',
        content: `消息发送失败: ${errorMessage}`,
      });

      toast.error(`消息发送失败: ${errorMessage}`);
      throw error;
    }
  }, [addMessage, updateStep, generateThreadId]);

  // 流式分析
  const streamAnalysis = useCallback(async function* (ticker: string, config: any = {}) {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        isStreaming: true,
        error: null,
        currentStep: '开始流式分析',
        progress: 0,
      }));

      // 生成新的线程ID
      generateThreadId();

      // 添加用户消息
      addMessage({
        type: 'human',
        content: `请流式分析股票 ${ticker}`,
        metadata: { ticker, config },
      });

      // 使用客户端进行流式分析
      for await (const chunk of langGraphClient.streamAnalysis(ticker, config, threadIdRef.current)) {
        // 更新步骤和进度
        setState(prev => ({
          ...prev,
          currentStep: chunk.currentStep || prev.currentStep,
          progress: chunk.progress || prev.progress,
        }));

        yield chunk;

        // 如果是最终结果，更新状态
        if (chunk.progress === 100 && chunk.content) {
          addMessage({
            type: 'ai',
            content: chunk.content,
            metadata: chunk,
          });

          setState(prev => ({
            ...prev,
            analysisResults: chunk.analysisResults,
            tradingDecision: chunk.tradingDecision,
          }));
        }
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        isStreaming: false,
        currentStep: '流式分析完成',
        progress: 100,
      }));

      toast.success('流式分析完成');

    } catch (error) {
      console.error('流式分析失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        isStreaming: false,
        error: errorMessage,
        currentStep: '流式分析失败',
        progress: 0,
      }));

      toast.error(`流式分析失败: ${errorMessage}`);
      throw error;
    }
  }, [addMessage, updateStep, generateThreadId]);

  // 获取代理状态
  const getAgentState = useCallback(async () => {
    if (!threadIdRef.current) return null;

    try {
      return await langGraphClient.getSessionState(threadIdRef.current);
    } catch (error) {
      console.error('获取代理状态失败:', error);
      return null;
    }
  }, []);

  // 清除对话
  const clearConversation = useCallback(async () => {
    // 清除后端会话
    if (threadIdRef.current) {
      try {
        await langGraphClient.clearSession(threadIdRef.current);
      } catch (error) {
        console.error('清除后端会话失败:', error);
      }
    }

    // 清除前端状态
    setState({
      messages: [],
      isProcessing: false,
      currentStep: '',
      analysisResults: null,
      tradingDecision: null,
      error: null,
      progress: 0,
      isStreaming: false,
      connectionStatus: 'disconnected',
      sessionInfo: {
        messageCount: 0,
      },
    });
    threadIdRef.current = '';
  }, []);

  // 重试最后一个操作
  const retry = useCallback(async () => {
    const lastHumanMessage = state.messages
      .filter(msg => msg.type === 'human')
      .pop();

    if (!lastHumanMessage) {
      toast.error('没有可重试的操作');
      return;
    }

    // 检查是否是分析请求
    if (lastHumanMessage.metadata?.ticker) {
      return analyzeStock(
        lastHumanMessage.metadata.ticker,
        lastHumanMessage.metadata.config
      );
    } else {
      return sendMessage(lastHumanMessage.content);
    }
  }, [state.messages, analyzeStock, sendMessage]);

  return {
    // 状态
    messages: state.messages,
    isProcessing: state.isProcessing,
    isStreaming: state.isStreaming,
    currentStep: state.currentStep,
    progress: state.progress,
    analysisResults: state.analysisResults,
    tradingDecision: state.tradingDecision,
    error: state.error,
    connectionStatus: state.connectionStatus,
    sessionInfo: state.sessionInfo,
    threadId: threadIdRef.current,

    // 操作
    analyzeStock,
    sendMessage,
    streamAnalysis,
    getAgentState,
    clearConversation,
    retry,

    // 实用工具
    addMessage,
    updateStep,
    generateThreadId,
  };
}
