import { NextRequest, NextResponse } from 'next/server';

// 模拟的状态存储（在实际应用中应该使用数据库）
const threadStates = new Map<string, any>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 获取线程状态
    const state = threadStates.get(threadId) || {
      threadId,
      messages: [],
      analysisResults: null,
      tradingDecision: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json(state);
  } catch (error) {
    console.error('获取状态失败:', error);
    return NextResponse.json(
      { error: '获取状态失败，请稍后重试' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { threadId, state } = await request.json();

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 更新线程状态
    const currentState = threadStates.get(threadId) || {};
    const updatedState = {
      ...currentState,
      ...state,
      threadId,
      updatedAt: new Date().toISOString(),
    };

    threadStates.set(threadId, updatedState);

    return NextResponse.json(updatedState);
  } catch (error) {
    console.error('更新状态失败:', error);
    return NextResponse.json(
      { error: '更新状态失败，请稍后重试' },
      { status: 500 }
    );
  }
}
