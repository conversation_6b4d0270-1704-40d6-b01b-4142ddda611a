import { NextRequest, NextResponse } from 'next/server';

// 简单的聊天响应生成器
function generateChatResponse(message: string): string {
  const lowerMessage = message.toLowerCase();
  
  // 股票相关问题
  if (lowerMessage.includes('股票') || lowerMessage.includes('分析')) {
    return '我可以帮您分析股票。请提供股票代码，我将为您进行全面的基本面、技术面和情绪分析。';
  }
  
  // 交易相关问题
  if (lowerMessage.includes('交易') || lowerMessage.includes('买入') || lowerMessage.includes('卖出')) {
    return '关于交易决策，我建议您考虑以下因素：1) 基本面分析 2) 技术指标 3) 市场情绪 4) 风险管理。您想了解哪个方面的详细信息？';
  }
  
  // 风险相关问题
  if (lowerMessage.includes('风险') || lowerMessage.includes('止损')) {
    return '风险管理是投资的重要组成部分。建议设置合理的止损位，控制仓位大小，并进行充分的分散投资。您的风险承受能力如何？';
  }
  
  // 市场相关问题
  if (lowerMessage.includes('市场') || lowerMessage.includes('趋势')) {
    return '市场分析需要综合考虑宏观经济、行业趋势、技术指标等多个维度。您想了解哪个具体市场或行业的情况？';
  }
  
  // 技术分析相关
  if (lowerMessage.includes('技术') || lowerMessage.includes('指标') || lowerMessage.includes('rsi') || lowerMessage.includes('macd')) {
    return '技术分析包括趋势分析、支撑阻力位、动量指标等。常用指标有RSI、MACD、布林带等。您想了解哪个具体指标？';
  }
  
  // 基本面分析相关
  if (lowerMessage.includes('基本面') || lowerMessage.includes('财报') || lowerMessage.includes('估值')) {
    return '基本面分析关注公司的财务状况、盈利能力、成长性等。主要指标包括P/E、P/B、ROE、营收增长率等。您想了解哪家公司的基本面？';
  }
  
  // 问候语
  if (lowerMessage.includes('你好') || lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    return '您好！我是您的专业交易分析助手。我可以帮您分析股票、评估风险、制定交易策略。请告诉我您需要什么帮助？';
  }
  
  // 帮助相关
  if (lowerMessage.includes('帮助') || lowerMessage.includes('help') || lowerMessage.includes('功能')) {
    return `我可以为您提供以下服务：
    
📊 **股票分析**: 提供基本面、技术面、情绪分析
📈 **交易建议**: 基于分析结果给出买入/卖出/持有建议
⚠️ **风险评估**: 评估投资风险和制定风险管理策略
📰 **市场洞察**: 分析市场趋势和新闻影响
🎯 **投资组合**: 帮助优化投资组合配置

请告诉我您想了解哪个方面，或者直接提供股票代码进行分析！`;
  }
  
  // 默认响应
  return '我理解您的问题。作为专业的交易分析助手，我可以帮您分析股票、评估风险、制定交易策略。请提供更具体的信息，比如股票代码或您关心的具体问题，我将为您提供详细的分析。';
}

// 模拟聊天处理
async function processChat(message: string, threadId: string) {
  // 模拟处理延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const response = generateChatResponse(message);
  
  return {
    content: response,
    metadata: {
      threadId,
      timestamp: new Date().toISOString(),
      messageType: 'chat_response',
    },
  };
}

export async function POST(request: NextRequest) {
  try {
    const { message, threadId } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: '消息内容不能为空' },
        { status: 400 }
      );
    }

    // 处理聊天消息
    const result = await processChat(message, threadId || `thread_${Date.now()}`);

    return NextResponse.json(result);
  } catch (error) {
    console.error('聊天处理失败:', error);
    return NextResponse.json(
      { error: '消息处理失败，请稍后重试' },
      { status: 500 }
    );
  }
}
