'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { TradingDashboard } from '@/components/dashboard/TradingDashboard';
import { WelcomeScreen } from '@/components/welcome/WelcomeScreen';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

export default function HomePage() {
  const [showDashboard, setShowDashboard] = useState(false);
  const [analysisConfig, setAnalysisConfig] = useState<any>(null);

  const handleStartAnalysis = (config: any) => {
    setAnalysisConfig(config);
    setShowDashboard(true);
  };

  const handleBackToWelcome = () => {
    setShowDashboard(false);
    setAnalysisConfig(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      <Header onBackToWelcome={showDashboard ? handleBackToWelcome : undefined} />
      
      <main className="container mx-auto px-4 py-8">
        <motion.div
          key={showDashboard ? 'dashboard' : 'welcome'}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
        >
          {showDashboard ? (
            <TradingDashboard 
              config={analysisConfig}
              onBack={handleBackToWelcome}
            />
          ) : (
            <WelcomeScreen onStartAnalysis={handleStartAnalysis} />
          )}
        </motion.div>
      </main>

      <Footer />
    </div>
  );
}
