'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PaperAirplaneIcon, 
  TrashIcon, 
  ArrowPathIcon,
  ChatBubbleLeftRightIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Badge } from '@/components/ui/Badge';
import { useLangGraphAgent } from '@/hooks/useLangGraphAgent';
import { formatDateTime } from '@/utils/helpers';

interface LangGraphChatProps {
  ticker?: string;
  onAnalysisComplete?: (result: any) => void;
  className?: string;
}

export function LangGraphChat({ ticker, onAnalysisComplete, className }: LangGraphChatProps) {
  const [inputMessage, setInputMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const {
    messages,
    isProcessing,
    currentStep,
    analysisResults,
    tradingDecision,
    error,
    threadId,
    analyzeStock,
    sendMessage,
    clearConversation,
    retry,
  } = useLangGraphAgent();

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 分析完成回调
  useEffect(() => {
    if (tradingDecision && onAnalysisComplete) {
      onAnalysisComplete({
        analysisResults,
        tradingDecision,
        messages,
      });
    }
  }, [tradingDecision, analysisResults, messages, onAnalysisComplete]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing) return;

    const message = inputMessage.trim();
    setInputMessage('');

    try {
      await sendMessage(message);
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  const handleAnalyzeStock = async () => {
    if (!ticker || isProcessing) return;

    try {
      await analyzeStock(ticker, {
        analysisType: 'comprehensive',
        includeRisk: true,
        includeSentiment: true,
      });
    } catch (error) {
      console.error('分析失败:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'human':
        return '👤';
      case 'ai':
        return '🤖';
      case 'tool':
        return '🔧';
      case 'system':
        return '⚙️';
      default:
        return '💬';
    }
  };

  const getMessageBgColor = (type: string) => {
    switch (type) {
      case 'human':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'ai':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'tool':
        return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800';
      case 'system':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <CpuChipIcon className="h-5 w-5 text-blue-600" />
            <span>LangGraph 智能分析</span>
            {threadId && (
              <Badge variant="info" size="sm">
                会话: {threadId.slice(-8)}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {ticker && (
              <Button
                size="sm"
                onClick={handleAnalyzeStock}
                disabled={isProcessing}
                className="flex items-center space-x-1"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                <span>分析 {ticker}</span>
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={retry}
              disabled={isProcessing || messages.length === 0}
            >
              <ArrowPathIcon className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={clearConversation}
              disabled={isProcessing}
            >
              <TrashIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {isProcessing && currentStep && (
          <div className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400">
            <LoadingSpinner size="sm" />
            <span>{currentStep}</span>
          </div>
        )}
        
        {error && (
          <div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
            错误: {error}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 消息列表 */}
        <div className="h-96 overflow-y-auto space-y-3 p-2">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`p-3 rounded-lg border ${getMessageBgColor(message.type)}`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 text-lg">
                    {getMessageIcon(message.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        {message.type === 'human' ? '用户' : 
                         message.type === 'ai' ? 'AI助手' :
                         message.type === 'tool' ? '工具' : '系统'}
                      </span>
                      <span className="text-xs text-slate-500">
                        {formatDateTime(message.timestamp.toISOString(), 'HH:mm:ss')}
                      </span>
                    </div>
                    
                    <div className="text-sm text-slate-800 dark:text-slate-200 whitespace-pre-wrap">
                      {message.content}
                    </div>
                    
                    {message.metadata && (
                      <details className="mt-2">
                        <summary className="text-xs text-slate-500 cursor-pointer">
                          元数据
                        </summary>
                        <pre className="text-xs text-slate-600 dark:text-slate-400 mt-1 p-2 bg-slate-100 dark:bg-slate-800 rounded overflow-auto">
                          {JSON.stringify(message.metadata, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {messages.length === 0 && (
            <div className="text-center text-slate-500 py-8">
              <CpuChipIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p>开始与 LangGraph 智能代理对话</p>
              <p className="text-sm mt-1">
                {ticker ? `输入消息或点击"分析 ${ticker}"开始` : '输入消息开始对话'}
              </p>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* 输入区域 */}
        <div className="flex space-x-2">
          <div className="flex-1">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入消息... (Shift+Enter 换行)"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={2}
              disabled={isProcessing}
            />
          </div>
          
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isProcessing}
            className="px-3"
          >
            {isProcessing ? (
              <LoadingSpinner size="sm" />
            ) : (
              <PaperAirplaneIcon className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {/* 快捷操作 */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setInputMessage('请解释一下当前的市场趋势')}
            disabled={isProcessing}
          >
            市场趋势
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setInputMessage('这只股票的风险如何？')}
            disabled={isProcessing}
          >
            风险评估
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setInputMessage('给我一些投资建议')}
            disabled={isProcessing}
          >
            投资建议
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
